{
  message: 'Test log',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 05:24:38'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 05:51:18'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 05:51:18'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `localhost:5432`
  
  Please make sure your database server is running at `localhost:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:205:5)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:43:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'error',
  message: 'Failed to start server',
  timestamp: '2025-06-10 05:51:22'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 05:52:10'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 05:52:10'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `localhost:5432`
  
  Please make sure your database server is running at `localhost:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:205:5)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:43:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'error',
  message: 'Failed to start server',
  timestamp: '2025-06-10 05:52:14'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 05:52:19'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 05:52:19'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `localhost:5432`
  
  Please make sure your database server is running at `localhost:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:208:5)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:43:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'error',
  message: 'Failed to start server',
  timestamp: '2025-06-10 05:52:23'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 05:52:57'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 05:52:57'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `localhost:5432`
  
  Please make sure your database server is running at `localhost:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:208:5)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:43:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'error',
  message: 'Failed to start server',
  timestamp: '2025-06-10 05:53:01'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:10:55'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:10:55'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `localhost:5432`
  
  Please make sure your database server is running at `localhost:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:208:5)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:43:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'error',
  message: 'Failed to start server',
  timestamp: '2025-06-10 13:10:59'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:11:16'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:11:16'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `localhost:5432`
  
  Please make sure your database server is running at `localhost:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:208:5)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:43:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'error',
  message: 'Failed to start server',
  timestamp: '2025-06-10 13:11:20'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:13:49'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:13:49'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `localhost:5432`
  
  Please make sure your database server is running at `localhost:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:208:5)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:43:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'error',
  message: 'Failed to start server',
  timestamp: '2025-06-10 13:13:53'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:14:41'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:14:41'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `localhost:5432`
  
  Please make sure your database server is running at `localhost:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:209:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:43:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-10 13:14:46'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:14:46'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:14:46'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:14:46'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:14:46'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:14:46'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:14:46'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:14:46'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:14:46'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:14:46'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:14:46'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:14:47'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:14:47'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:14:58'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:14:58'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `localhost:5432`
  
  Please make sure your database server is running at `localhost:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:209:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:43:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-10 13:15:02'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:15:02'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:15:02'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:15:02'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:15:02'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:15:02'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:15:02'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:15:03'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:15:03'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:15:03'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:15:03'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:15:03'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:15:03'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:15:04'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:15:04'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:15:04'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:15:04'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:15:05'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:15:05'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:15:06'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:15:06'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:15:07'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:15:07'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:15:08'
}
{
  message: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:15:08'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 13:15:08'
}
{
  service: 'freela-api',
  environment: 'development',
  error: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  message: 'Redis: Failed to connect',
  timestamp: '2025-06-10 13:15:08'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:15:08'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:15:08'
}
{
  message: '🚀 Server running on port 3001',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:15:08'
}
{
  message: '📚 API Documentation: http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:15:08'
}
{
  message: '🏥 Health Check: http://localhost:3001/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:15:08'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 13:15:08'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 503,
  duration: '4095ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-10 13:15:33'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 200,
  duration: '1ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 13:15:39'
}
{
  service: 'freela-api',
  environment: 'development',
  message: 'Operational Error Route GET /favicon.ico not found',
  statusCode: 404,
  code: 'ROUTE_NOT_FOUND',
  path: '/favicon.ico',
  method: 'GET',
  userId: undefined,
  level: 'warn',
  timestamp: '2025-06-10 13:15:41'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/favicon.ico',
  statusCode: 404,
  duration: '9ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-10 13:15:41'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 301,
  duration: '3ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 13:15:41'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 200,
  duration: '4ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 13:15:41'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui-init.js',
  statusCode: 200,
  duration: '5ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 13:15:41'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui.css',
  statusCode: 200,
  duration: '23ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 13:15:41'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui-standalone-preset.js',
  statusCode: 200,
  duration: '27ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 13:15:41'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui-bundle.js',
  statusCode: 200,
  duration: '67ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 13:15:42'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/favicon-32x32.png',
  statusCode: 200,
  duration: '2ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 13:15:43'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/favicon-32x32.png',
  statusCode: 304,
  duration: '1ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 13:15:43'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 200,
  duration: '1ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 13:15:47'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:43:55'
}
{
  message: 'Swagger documentation available at http://localhost:3001/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:43:55'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `localhost:5432`
  
  Please make sure your database server is running at `localhost:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:209:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:43:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-10 14:43:59'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:43:59'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:43:59'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:43:59'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:43:59'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:43:59'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:43:59'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:43:59'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:43:59'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:44:00'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:00'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:44:00'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:00'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:44:01'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:01'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:44:01'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:01'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:44:02'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:02'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:44:03'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:03'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:44:04'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:04'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:44:05'
}
{
  message: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:44:05'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:44:51'
}
{
  message: 'Swagger documentation available at http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:44:51'
}
{
  service: 'freela-api',
  environment: 'development',
  error: PrismaClientInitializationError: Can't reach database server at `localhost:5432`
  
  Please make sure your database server is running at `localhost:5432`.
      at t (C:\Users\<USER>\Documents\Freela\node_modules\.prisma\client\runtime\library.js:112:2488)
      at async connectDatabase (C:\Users\<USER>\Documents\Freela\packages\database\src\client.ts:38:5)
      at async App.initialize (C:\Users\<USER>\Documents\Freela\apps\api\src\app.ts:209:7)
      at async startServer (C:\Users\<USER>\Documents\Freela\apps\api\src\index.ts:43:5) {
    clientVersion: '5.22.0',
    errorCode: 'P1001'
  },
  level: 'warn',
  message: '⚠️ Database connection failed, continuing without database',
  timestamp: '2025-06-10 14:44:55'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:44:55'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:55'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:44:55'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:55'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:44:55'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:55'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:44:55'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:55'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:44:56'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:56'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:44:56'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:56'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:44:56'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:56'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:44:57'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:57'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:44:58'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:58'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:44:59'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:44:59'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:45:00'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:45:00'
}
{
  message: 'Redis: Reconnecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:45:01'
}
{
  message: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:45:01'
}
{
  service: 'freela-api',
  environment: 'development',
  error: '',
  level: 'error',
  message: 'Redis: Connection error',
  timestamp: '2025-06-10 14:45:01'
}
{
  service: 'freela-api',
  environment: 'development',
  error: 'Redis: Maximum reconnection attempts reached',
  level: 'error',
  message: 'Redis: Failed to connect',
  timestamp: '2025-06-10 14:45:01'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:45:01'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:45:01'
}
{
  message: '🚀 Server running on port 3000',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:45:01'
}
{
  message: '📚 API Documentation: http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:45:01'
}
{
  message: '🏥 Health Check: http://localhost:3000/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:45:01'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 14:45:01'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 503,
  duration: '4079ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-10 14:45:16'
}
{
  service: 'freela-api',
  environment: 'development',
  message: 'Operational Error Route GET /favicon.ico not found',
  statusCode: 404,
  code: 'ROUTE_NOT_FOUND',
  path: '/favicon.ico',
  method: 'GET',
  userId: undefined,
  level: 'warn',
  timestamp: '2025-06-10 14:45:17'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/favicon.ico',
  statusCode: 404,
  duration: '9ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-10 14:45:17'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 301,
  duration: '3ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 14:45:17'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 200,
  duration: '3ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 14:45:18'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui-init.js',
  statusCode: 200,
  duration: '6ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 14:45:18'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui.css',
  statusCode: 200,
  duration: '20ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 14:45:18'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui-standalone-preset.js',
  statusCode: 200,
  duration: '21ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 14:45:18'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui-bundle.js',
  statusCode: 200,
  duration: '51ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 14:45:18'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/favicon-32x32.png',
  statusCode: 200,
  duration: '1ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 14:45:19'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/favicon-32x32.png',
  statusCode: 304,
  duration: '2ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 14:45:19'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 15:50:17'
}
{
  message: 'Swagger documentation available at http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 15:50:17'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 15:50:17'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 15:50:17'
}
{
  message: 'Redis: Connected and ready',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 15:50:17'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 15:50:17'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 15:50:17'
}
{
  message: '🚀 Server running on port 3000',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 15:50:17'
}
{
  message: '📚 API Documentation: http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 15:50:17'
}
{
  message: '🏥 Health Check: http://localhost:3000/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 15:50:17'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 15:50:17'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 304,
  duration: '4ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 15:53:29'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui.css',
  statusCode: 304,
  duration: '2ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 15:53:29'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui-bundle.js',
  statusCode: 304,
  duration: '2ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 15:53:29'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui-standalone-preset.js',
  statusCode: 304,
  duration: '3ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 15:53:29'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui-init.js',
  statusCode: 304,
  duration: '1ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 15:53:29'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/favicon-32x32.png',
  statusCode: 304,
  duration: '1ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 15:53:30'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/favicon-32x32.png',
  statusCode: 304,
  duration: '1ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 15:53:31'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 15:57:35'
}
{
  message: 'Swagger documentation available at http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 15:57:35'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 15:57:35'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 15:57:35'
}
{
  message: 'Redis: Connected and ready',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 15:57:35'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '59ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 15:58:41'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 304,
  duration: '3ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 15:59:12'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui.css',
  statusCode: 304,
  duration: '3ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 15:59:12'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui-bundle.js',
  statusCode: 304,
  duration: '4ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 15:59:12'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui-standalone-preset.js',
  statusCode: 304,
  duration: '2ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 15:59:12'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui-init.js',
  statusCode: 304,
  duration: '2ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 15:59:12'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/favicon-32x32.png',
  statusCode: 304,
  duration: '2ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 15:59:13'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '5ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 15:59:20'
}
{
  service: 'freela-api',
  environment: 'development',
  source: 'body',
  errors: [
    {
      field: 'password',
      message: 'Password must contain at least one uppercase letter',
      code: 'invalid_string'
    },
    {
      field: 'password',
      message: 'Password must contain at least one special character',
      code: 'invalid_string'
    },
    { field: 'acceptTerms', message: 'Required', code: 'invalid_type' }
  ],
  data: {
    email: '<EMAIL>',
    role: 'CLIENT',
    password: 'password123',
    firstName: 'Test',
    lastName: 'User'
  },
  endpoint: '/register',
  method: 'POST',
  level: 'warn',
  message: 'Validation failed',
  timestamp: '2025-06-10 15:59:27'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'POST',
  url: '/register',
  statusCode: 400,
  duration: '6ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
  userId: undefined,
  level: 'warn',
  message: 'HTTP Request',
  timestamp: '2025-06-10 15:59:27'
}
{
  service: 'freela-api',
  environment: 'development',
  action: 'user_registered',
  userId: 'cmbql82nc0000fh9zkti45s42',
  details: { email: '<EMAIL>', role: 'CLIENT', language: 'ar' },
  timestamp: '2025-06-10 15:59:45',
  level: 'info',
  message: 'User Action'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'POST',
  url: '/register',
  statusCode: 201,
  duration: '385ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 15:59:45'
}
{
  service: 'freela-api',
  environment: 'development',
  userId: 'cmbqkq8ab0000wvkt45ce5wz6',
  sessionId: 'Ds8zroeacLM-bKqxkxY-i',
  level: 'info',
  message: 'Session created',
  timestamp: '2025-06-10 15:59:54'
}
{
  service: 'freela-api',
  environment: 'development',
  action: 'user_logged_in',
  userId: 'cmbqkq8ab0000wvkt45ce5wz6',
  details: {
    ip: '::1',
    userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
    rememberMe: false
  },
  timestamp: '2025-06-10 15:59:54',
  level: 'info',
  message: 'User Action'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'POST',
  url: '/login',
  statusCode: 200,
  duration: '330ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT; Windows NT 10.0; de-DE) WindowsPowerShell/5.1.26100.4202',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 15:59:54'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 16:12:11'
}
{
  message: 'Swagger documentation available at http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 16:12:11'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 16:12:11'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 16:12:11'
}
{
  message: 'Redis: Connected and ready',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 16:12:11'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 16:12:11'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 16:12:11'
}
{
  message: '🚀 Server running on port 3000',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 16:12:11'
}
{
  message: '📚 API Documentation: http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 16:12:11'
}
{
  message: '🏥 Health Check: http://localhost:3000/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 16:12:11'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 16:12:11'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '28ms',
  ip: '::ffff:127.0.0.1',
  userAgent: 'Augment-VSCode/1.0',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 16:13:32'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 19:24:20'
}
{
  message: 'Swagger documentation available at http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 19:24:20'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 19:24:21'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 19:24:21'
}
{
  message: 'Redis: Connected and ready',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 19:24:21'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 19:24:21'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 19:24:21'
}
{
  message: '🚀 Server running on port 3000',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 19:24:21'
}
{
  message: '📚 API Documentation: http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 19:24:21'
}
{
  message: '🏥 Health Check: http://localhost:3000/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 19:24:21'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 19:24:21'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 304,
  duration: '10ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 19:26:59'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui.css',
  statusCode: 304,
  duration: '4ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 19:26:59'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui-bundle.js',
  statusCode: 304,
  duration: '5ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 19:26:59'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui-standalone-preset.js',
  statusCode: 304,
  duration: '4ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 19:26:59'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/swagger-ui-init.js',
  statusCode: 304,
  duration: '2ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 19:26:59'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/favicon-32x32.png',
  statusCode: 304,
  duration: '1ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 19:27:01'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/health',
  statusCode: 200,
  duration: '40ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 19:27:06'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 20:04:29'
}
{
  message: 'Swagger documentation available at http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 20:04:29'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 20:04:29'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 20:04:29'
}
{
  message: 'Redis: Connected and ready',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 20:04:29'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 20:04:29'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 20:04:29'
}
{
  message: '🚀 Server running on port 3000',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 20:04:29'
}
{
  message: '📚 API Documentation: http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 20:04:29'
}
{
  message: '🏥 Health Check: http://localhost:3000/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 20:04:29'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 20:04:29'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 20:15:18'
}
{
  message: 'Swagger documentation available at http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 20:15:18'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 20:15:19'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 20:15:19'
}
{
  message: 'Redis: Connected and ready',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 20:15:19'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 20:21:17'
}
{
  message: 'Swagger documentation available at http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 20:21:17'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 20:21:17'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 20:21:17'
}
{
  message: 'Redis: Connected and ready',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 20:21:17'
}
{
  service: 'freela-api',
  environment: 'development',
  method: 'GET',
  url: '/',
  statusCode: 200,
  duration: '10ms',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  userId: undefined,
  level: 'info',
  message: 'HTTP Request',
  timestamp: '2025-06-10 21:39:09'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 22:40:02'
}
{
  message: 'Swagger documentation available at http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 22:40:02'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 22:40:02'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 22:40:02'
}
{
  message: 'Redis: Connected and ready',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 22:40:02'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 22:40:02'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 22:40:02'
}
{
  message: '🚀 Server running on port 3000',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 22:40:02'
}
{
  message: '📚 API Documentation: http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 22:40:02'
}
{
  message: '🏥 Health Check: http://localhost:3000/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 22:40:02'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-10 22:40:02'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 01:01:43'
}
{
  message: 'Swagger documentation available at http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 01:01:43'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 01:01:43'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 01:01:43'
}
{
  message: 'Redis: Connected and ready',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 01:01:43'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 01:20:47'
}
{
  message: 'Swagger documentation available at http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 01:20:47'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 01:20:47'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 01:20:47'
}
{
  message: 'Redis: Connected and ready',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 01:20:47'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 02:32:15'
}
{
  message: 'Swagger documentation available at http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 02:32:15'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 02:32:15'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 02:32:15'
}
{
  message: 'Redis: Connected and ready',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 02:32:15'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 02:55:03'
}
{
  message: 'Swagger documentation available at http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 02:55:03'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 02:55:04'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 02:55:04'
}
{
  message: 'Redis: Connected and ready',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 02:55:04'
}
{
  message: 'Initializing application...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 18:47:14'
}
{
  message: 'Swagger documentation available at http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 18:47:14'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 18:47:15'
}
{
  message: 'Redis: Connecting...',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 18:47:15'
}
{
  message: 'Redis: Connected and ready',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 18:47:15'
}
{
  message: '✅ Redis connected successfully',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 18:47:15'
}
{
  message: '✅ Application initialized successfully.',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 18:47:15'
}
{
  message: '🚀 Server running on port 3000',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 18:47:15'
}
{
  message: '📚 API Documentation: http://localhost:3000/api/v1/docs',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 18:47:15'
}
{
  message: '🏥 Health Check: http://localhost:3000/health',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 18:47:15'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'freela-api',
  environment: 'development',
  timestamp: '2025-06-11 18:47:15'
}

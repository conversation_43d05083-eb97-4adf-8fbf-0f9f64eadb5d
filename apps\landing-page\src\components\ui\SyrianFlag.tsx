import React from 'react';
import Image from 'next/image';

interface SyrianFlagProps {
  width?: number;
  height?: number;
  className?: string;
}

export default function SyrianFlag({
  width = 180,
  height = 120,
  className = ''
}: SyrianFlagProps) {
  return (
    <div className={`inline-block ${className}`}>
      <div
        className="relative overflow-hidden rounded-lg shadow-lg"
        style={{ width, height }}
      >
        <Image
          src="/thisflag.png"
          alt="Syrian Flag"
          width={width}
          height={height}
          className="object-contain w-full h-full"
          priority
        />
        {/* Subtle overlay for premium effect */}
        <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-black/5 pointer-events-none" />
        {/* Glass effect border */}
        <div className="absolute inset-0 border border-white/10 rounded-lg pointer-events-none" />
      </div>
    </div>
  );
}

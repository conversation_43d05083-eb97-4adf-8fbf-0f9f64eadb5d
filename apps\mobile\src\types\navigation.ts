import { NavigatorScreenParams, StackNavigationProp } from '@react-navigation/native';
import { BottomTabNavigationProp } from '@react-navigation/bottom-tabs';

// Root Stack
export type RootStackParamList = {
  Onboarding: undefined;
  Auth: NavigatorScreenParams<AuthStackParamList>;
  Main: NavigatorScreenParams<MainStackParamList>;
};

// Auth Stack
export type AuthStackParamList = {
  Login: undefined;
  Register: undefined;
  RoleSelection: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    phone?: string;
  };
  ForgotPassword: undefined;
  VerifyEmail: {
    email: string;
  };
  ResetPassword: {
    token: string;
  };
};

// Main Stack
export type MainStackParamList = {
  Tabs: NavigatorScreenParams<MainTabParamList>;
  ServiceDetails: {
    serviceId: string;
  };
  ExpertProfile: {
    expertId: string;
  };
  BookingDetails: {
    bookingId: string;
  };
  ChatConversation: {
    conversationId: string;
  };
  EditProfile: undefined;
  Settings: undefined;
  Notifications: undefined;
};

// Client Tabs
export type ClientTabParamList = {
  Home: undefined;
  Search: undefined;
  Bookings: undefined;
  Chat: undefined;
  Profile: undefined;
};

// Expert Tabs
export type ExpertTabParamList = {
  Dashboard: undefined;
  Services: undefined;
  Bookings: undefined;
  Chat: undefined;
  Earnings: undefined;
};

// Main Tabs (Union of Client and Expert)
export type MainTabParamList = ClientTabParamList | ExpertTabParamList;

// Navigation prop types
export type RootNavigationProp = StackNavigationProp<RootStackParamList>;
export type AuthNavigationProp = StackNavigationProp<AuthStackParamList>;
export type MainNavigationProp = StackNavigationProp<MainStackParamList>;
export type ClientTabNavigationProp = BottomTabNavigationProp<ClientTabParamList>;
export type ExpertTabNavigationProp = BottomTabNavigationProp<ExpertTabParamList>;

// Screen props helper types
export type RootStackScreenProps<T extends keyof RootStackParamList> = {
  navigation: StackNavigationProp<RootStackParamList, T>;
  route: {
    key: string;
    name: T;
    params: RootStackParamList[T];
  };
};

export type AuthStackScreenProps<T extends keyof AuthStackParamList> = {
  navigation: StackNavigationProp<AuthStackParamList, T>;
  route: {
    key: string;
    name: T;
    params: AuthStackParamList[T];
  };
};

export type MainStackScreenProps<T extends keyof MainStackParamList> = {
  navigation: StackNavigationProp<MainStackParamList, T>;
  route: {
    key: string;
    name: T;
    params: MainStackParamList[T];
  };
};

export type ClientTabScreenProps<T extends keyof ClientTabParamList> = {
  navigation: BottomTabNavigationProp<ClientTabParamList, T>;
  route: {
    key: string;
    name: T;
    params: ClientTabParamList[T];
  };
};

export type ExpertTabScreenProps<T extends keyof ExpertTabParamList> = {
  navigation: BottomTabNavigationProp<ExpertTabParamList, T>;
  route: {
    key: string;
    name: T;
    params: ExpertTabParamList[T];
  };
};
